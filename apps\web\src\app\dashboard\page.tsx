"use client";
import { authClient } from "@/lib/auth-client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Building, FileText, Users, Plus } from "lucide-react";
import Link from "next/link";

export default function Dashboard() {
  const router = useRouter();
  const { data: session, isPending } = authClient.useSession();

  useEffect(() => {
    if (!session && !isPending) {
      router.push("/login");
    }

    const fetchData = async () => {
      if (!session) return;
      const res = await fetch("/api/property", {
        headers: {
          Authorization: `Bearer ${session?.session.token}`,
        },
      });
      const data = await res.json();
    };
    fetchData();
  }, [session, isPending]);

  if (isPending) {
    return <div>Loading...</div>;
  }

  return (
    <main className="min-h-screen bg-background py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2">
            Landlord Management System
          </h1>
          <p className="text-xl text-muted-foreground">
            Manage your properties, leases, and tenants efficiently
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <Building className="w-12 h-12 mx-auto mb-4 text-primary" />
              <CardTitle>Properties</CardTitle>
              <p className="text-sm text-muted-foreground">
                Add and manage your rental properties
              </p>
            </CardHeader>
            <CardContent>
              <Link href="/properties">
                <Button className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Manage Properties
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <FileText className="w-12 h-12 mx-auto mb-4 text-primary" />
              <CardTitle>Leases</CardTitle>
              <p className="text-sm text-muted-foreground">
                Create and manage lease agreements
              </p>
            </CardHeader>
            <CardContent>
              <Link href="/leases">
                <Button className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Manage Leases
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <Users className="w-12 h-12 mx-auto mb-4 text-primary" />
              <CardTitle>Tenants</CardTitle>
              <p className="text-sm text-muted-foreground">
                Add and manage tenant information
              </p>
            </CardHeader>
            <CardContent>
              <Link href="/tenants">
                <Button className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Manage Tenants
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Properties
              </CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Properties in your portfolio
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Leases
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Lease agreements created
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Tenants
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Tenants registered
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
