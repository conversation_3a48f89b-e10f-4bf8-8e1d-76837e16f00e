{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/hooks/useProperties.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\";\nimport { authClient } from \"@/lib/auth-client\";\n\nexport interface Property {\n  id: string;\n  property_name: string;\n  plot_number: string;\n  street_name: string;\n  city: string;\n  state: string;\n  country: string;\n  bedrooms: number;\n  bathrooms: number;\n  base_rent?: number;\n  base_deposit?: number;\n  currency?: string;\n  listing_date: string;\n  vacant: boolean;\n}\n\nexport interface PropertyResponse {\n  success: boolean;\n  data: Property[];\n  error?: string;\n}\n\nconst fetchProperties = async (token: string): Promise<PropertyResponse> => {\n  const response = await fetch(\"/api/property\", {\n    headers: {\n      Authorization: `Bearer ${token}`,\n      \"Content-Type\": \"application/json\",\n    },\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch properties: ${response.statusText}`);\n  }\n\n  return response.json();\n};\n\nexport const useProperties = () => {\n  const { data: session } = authClient.useSession();\n\n  return useQuery({\n    queryKey: [\"properties\"],\n    queryFn: () => fetchProperties(session?.session.token!),\n    enabled: !!session?.session.token,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    gcTime: 10 * 60 * 1000, // 10 minutes\n  });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAyBA,MAAM,kBAAkB,OAAO;IAC7B,MAAM,WAAW,MAAM,MAAM,iBAAiB;QAC5C,SAAS;YACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAChC,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;IACtE;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,2IAAA,CAAA,aAAU,CAAC,UAAU;IAE/C,OAAO,CAAA,GAAA,wQAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAa;QACxB,SAAS,IAAM,gBAAgB,SAAS,QAAQ;QAChD,SAAS,CAAC,CAAC,SAAS,QAAQ;QAC5B,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AACF", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/contexts/PropertyContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, ReactNode } from \"react\";\nimport { useProperties, type Property, type PropertyResponse } from \"@/hooks/useProperties\";\n\ninterface PropertyContextType {\n  properties: Property[];\n  isLoading: boolean;\n  error: Error | null;\n  propertyCount: number;\n  refetch: () => void;\n}\n\nconst PropertyContext = createContext<PropertyContextType | undefined>(undefined);\n\nexport const PropertyProvider = ({ children }: { children: ReactNode }) => {\n  const { \n    data: propertiesResponse, \n    isLoading, \n    error,\n    refetch \n  } = useProperties();\n\n  const properties = propertiesResponse?.success ? propertiesResponse.data : [];\n  const propertyCount = properties.length;\n\n  const value: PropertyContextType = {\n    properties,\n    isLoading,\n    error,\n    propertyCount,\n    refetch,\n  };\n\n  return (\n    <PropertyContext.Provider value={value}>\n      {children}\n    </PropertyContext.Provider>\n  );\n};\n\nexport const usePropertyContext = () => {\n  const context = useContext(PropertyContext);\n  if (context === undefined) {\n    throw new Error(\"usePropertyContext must be used within a PropertyProvider\");\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAaA,MAAM,gCAAkB,CAAA,GAAA,0QAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAA2B;IACpE,MAAM,EACJ,MAAM,kBAAkB,EACxB,SAAS,EACT,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,aAAa,oBAAoB,UAAU,mBAAmB,IAAI,GAAG,EAAE;IAC7E,MAAM,gBAAgB,WAAW,MAAM;IAEvC,MAAM,QAA6B;QACjC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,mTAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,MAAM,qBAAqB;IAChC,MAAM,UAAU,CAAA,GAAA,0QAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,0OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,8PAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/PropertyList.tsx"], "sourcesContent": ["\"use client\";\n\nimport { usePropertyContext } from \"@/contexts/PropertyContext\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Building, MapPin, Bed, Bath, DollarSign } from \"lucide-react\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\n\nexport default function PropertyList() {\n  const { properties, isLoading, error } = usePropertyContext();\n\n  if (isLoading) {\n    return (\n      <div className=\"space-y-4\">\n        <h2 className=\"text-2xl font-bold\">Your Properties</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {[...Array(3)].map((_, i) => (\n            <Card key={i}>\n              <CardHeader>\n                <Skeleton className=\"h-6 w-3/4\" />\n                <Skeleton className=\"h-4 w-1/2\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-2\">\n                  <Skeleton className=\"h-4 w-full\" />\n                  <Skeleton className=\"h-4 w-2/3\" />\n                  <Skeleton className=\"h-4 w-1/2\" />\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"space-y-4\">\n        <h2 className=\"text-2xl font-bold\">Your Properties</h2>\n        <Card>\n          <CardContent className=\"pt-6\">\n            <p className=\"text-center text-muted-foreground\">\n              Failed to load properties. Please try again later.\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  if (properties.length === 0) {\n    return (\n      <div className=\"space-y-4\">\n        <h2 className=\"text-2xl font-bold\">Your Properties</h2>\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"text-center\">\n              <Building className=\"mx-auto h-12 w-12 text-muted-foreground mb-4\" />\n              <p className=\"text-lg font-medium\">No properties yet</p>\n              <p className=\"text-muted-foreground\">\n                Start by adding your first property to get started.\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <h2 className=\"text-2xl font-bold\">Your Properties</h2>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {properties.map((property) => (\n          <Card key={property.id} className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-start justify-between\">\n                <CardTitle className=\"text-lg\">{property.property_name}</CardTitle>\n                <Badge variant={property.vacant ? \"secondary\" : \"default\"}>\n                  {property.vacant ? \"Vacant\" : \"Occupied\"}\n                </Badge>\n              </div>\n              <div className=\"flex items-center text-sm text-muted-foreground\">\n                <MapPin className=\"h-4 w-4 mr-1\" />\n                {property.city}, {property.state}\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <div className=\"flex items-center\">\n                    <Bed className=\"h-4 w-4 mr-1\" />\n                    {property.bedrooms} bed\n                  </div>\n                  <div className=\"flex items-center\">\n                    <Bath className=\"h-4 w-4 mr-1\" />\n                    {property.bathrooms} bath\n                  </div>\n                </div>\n                {property.base_rent && (\n                  <div className=\"flex items-center text-sm\">\n                    <DollarSign className=\"h-4 w-4 mr-1\" />\n                    {property.base_rent} {property.currency || \"USD\"}/month\n                  </div>\n                )}\n                <p className=\"text-xs text-muted-foreground\">\n                  {property.plot_number} {property.street_name}\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IAE1D,IAAI,WAAW;QACb,qBACE,mTAAC;YAAI,WAAU;;8BACb,mTAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,mTAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,mTAAC,+IAAA,CAAA,OAAI;;8CACH,mTAAC,+IAAA,CAAA,aAAU;;sDACT,mTAAC,mJAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,mTAAC,mJAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,mTAAC,+IAAA,CAAA,cAAW;8CACV,cAAA,mTAAC;wCAAI,WAAU;;0DACb,mTAAC,mJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,mTAAC,mJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,mTAAC,mJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;2BATf;;;;;;;;;;;;;;;;IAiBrB;IAEA,IAAI,OAAO;QACT,qBACE,mTAAC;YAAI,WAAU;;8BACb,mTAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,mTAAC,+IAAA,CAAA,OAAI;8BACH,cAAA,mTAAC,+IAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,mTAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;IAO3D;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,qBACE,mTAAC;YAAI,WAAU;;8BACb,mTAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,mTAAC,+IAAA,CAAA,OAAI;8BACH,cAAA,mTAAC,+IAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,mTAAC;4BAAI,WAAU;;8CACb,mTAAC,2RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,mTAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,mTAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQjD;IAEA,qBACE,mTAAC;QAAI,WAAU;;0BACb,mTAAC;gBAAG,WAAU;0BAAqB;;;;;;0BACnC,mTAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,mTAAC,+IAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAChC,mTAAC,+IAAA,CAAA,aAAU;;kDACT,mTAAC;wCAAI,WAAU;;0DACb,mTAAC,+IAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,SAAS,aAAa;;;;;;0DACtD,mTAAC,gJAAA,CAAA,QAAK;gDAAC,SAAS,SAAS,MAAM,GAAG,cAAc;0DAC7C,SAAS,MAAM,GAAG,WAAW;;;;;;;;;;;;kDAGlC,mTAAC;wCAAI,WAAU;;0DACb,mTAAC,2RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,SAAS,IAAI;4CAAC;4CAAG,SAAS,KAAK;;;;;;;;;;;;;0CAGpC,mTAAC,+IAAA,CAAA,cAAW;0CACV,cAAA,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC;oDAAI,WAAU;;sEACb,mTAAC,iRAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd,SAAS,QAAQ;wDAAC;;;;;;;8DAErB,mTAAC;oDAAI,WAAU;;sEACb,mTAAC,mRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,SAAS,SAAS;wDAAC;;;;;;;;;;;;;wCAGvB,SAAS,SAAS,kBACjB,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,mSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDACrB,SAAS,SAAS;gDAAC;gDAAE,SAAS,QAAQ,IAAI;gDAAM;;;;;;;sDAGrD,mTAAC;4CAAE,WAAU;;gDACV,SAAS,WAAW;gDAAC;gDAAE,SAAS,WAAW;;;;;;;;;;;;;;;;;;;uBAhCzC,SAAS,EAAE;;;;;;;;;;;;;;;;AAyChC", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\nimport { authClient } from \"@/lib/auth-client\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { Building, FileText, Users, Plus, AlertCircle, RefreshCw } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { PropertyProvider, usePropertyContext } from \"@/contexts/PropertyContext\";\nimport PropertyList from \"@/components/PropertyList\";\nimport { toast } from \"sonner\";\n\nfunction DashboardContent() {\n  const { propertyCount, isLoading: isLoadingProperties, error: propertiesError, refetch } = usePropertyContext();\n\n  // Show error toast if there's an error fetching properties\n  useEffect(() => {\n    if (propertiesError) {\n      toast.error(\"Failed to load properties. Please try again.\");\n    }\n  }, [propertiesError]);\n\n  const handleRefresh = () => {\n    refetch();\n    toast.success(\"Properties refreshed!\");\n  };\n\n  return (\n    <main className=\"min-h-screen bg-background py-8\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-8\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div></div>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleRefresh}\n              disabled={isLoadingProperties}\n              className=\"flex items-center gap-2\"\n            >\n              <RefreshCw className={`h-4 w-4 ${isLoadingProperties ? 'animate-spin' : ''}`} />\n              Refresh Data\n            </Button>\n          </div>\n          <h1 className=\"text-4xl font-bold mb-2\">\n            Landlord Management System\n          </h1>\n          <p className=\"text-xl text-muted-foreground\">\n            Manage your properties, leases, and tenants efficiently\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\">\n          <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n            <CardHeader className=\"text-center\">\n              <Building className=\"w-12 h-12 mx-auto mb-4 text-primary\" />\n              <CardTitle>Properties</CardTitle>\n              <p className=\"text-sm text-muted-foreground\">\n                Add and manage your rental properties\n              </p>\n            </CardHeader>\n            <CardContent>\n              <Link href=\"/properties\">\n                <Button className=\"w-full\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  Manage Properties\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n            <CardHeader className=\"text-center\">\n              <FileText className=\"w-12 h-12 mx-auto mb-4 text-primary\" />\n              <CardTitle>Leases</CardTitle>\n              <p className=\"text-sm text-muted-foreground\">\n                Create and manage lease agreements\n              </p>\n            </CardHeader>\n            <CardContent>\n              <Link href=\"/leases\">\n                <Button className=\"w-full\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  Manage Leases\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n            <CardHeader className=\"text-center\">\n              <Users className=\"w-12 h-12 mx-auto mb-4 text-primary\" />\n              <CardTitle>Tenants</CardTitle>\n              <p className=\"text-sm text-muted-foreground\">\n                Add and manage tenant information\n              </p>\n            </CardHeader>\n            <CardContent>\n              <Link href=\"/tenants\">\n                <Button className=\"w-full\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  Manage Tenants\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Total Properties\n              </CardTitle>\n              <Building className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              {isLoadingProperties ? (\n                <Skeleton className=\"h-8 w-16 mb-1\" />\n              ) : propertiesError ? (\n                <div className=\"flex items-center gap-2 text-destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span className=\"text-sm\">Error</span>\n                </div>\n              ) : (\n                <div className=\"text-2xl font-bold\">{propertyCount}</div>\n              )}\n              <p className=\"text-xs text-muted-foreground\">\n                Properties in your portfolio\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Active Leases\n              </CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Lease agreements created\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Total Tenants\n              </CardTitle>\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Tenants registered\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Property List Section */}\n        <div className=\"mt-12\">\n          <PropertyList />\n        </div>\n      </div>\n    </main>\n  );\n}\n\nexport default function Dashboard() {\n  const router = useRouter();\n  const { data: session, isPending } = authClient.useSession();\n\n  useEffect(() => {\n    if (!session && !isPending) {\n      router.push(\"/login\");\n    }\n  }, [session, isPending, router]);\n\n  if (isPending) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <PropertyProvider>\n      <DashboardContent />\n    </PropertyProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;;AAaA,SAAS;IACP,MAAM,EAAE,aAAa,EAAE,WAAW,mBAAmB,EAAE,OAAO,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IAE5G,2DAA2D;IAC3D,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,8MAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,gBAAgB;QACpB;QACA,8MAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,mTAAC;QAAK,WAAU;kBACd,cAAA,mTAAC;YAAI,WAAU;;8BACb,mTAAC;oBAAI,WAAU;;sCACb,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;;;;;8CACD,mTAAC,iJAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,mTAAC,iSAAA,CAAA,YAAS;4CAAC,WAAW,CAAC,QAAQ,EAAE,sBAAsB,iBAAiB,IAAI;;;;;;wCAAI;;;;;;;;;;;;;sCAIpF,mTAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAGxC,mTAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAK/C,mTAAC;oBAAI,WAAU;;sCACb,mTAAC,+IAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,mTAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,mTAAC,2RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,mTAAC,+IAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,mTAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,mTAAC,+IAAA,CAAA,cAAW;8CACV,cAAA,mTAAC,iOAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,mTAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,mTAAC,mRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;sCAOzC,mTAAC,+IAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,mTAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,mTAAC,+RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,mTAAC,+IAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,mTAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,mTAAC,+IAAA,CAAA,cAAW;8CACV,cAAA,mTAAC,iOAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,mTAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,mTAAC,mRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;sCAOzC,mTAAC,+IAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,mTAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,mTAAC,qRAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,mTAAC,+IAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,mTAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,mTAAC,+IAAA,CAAA,cAAW;8CACV,cAAA,mTAAC,iOAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,mTAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,mTAAC,mRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,mTAAC;oBAAI,WAAU;;sCACb,mTAAC,+IAAA,CAAA,OAAI;;8CACH,mTAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,mTAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,mTAAC,2RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,mTAAC,+IAAA,CAAA,cAAW;;wCACT,oCACC,mTAAC,mJAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;mDAClB,gCACF,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,qSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,mTAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;iEAG5B,mTAAC;4CAAI,WAAU;sDAAsB;;;;;;sDAEvC,mTAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,mTAAC,+IAAA,CAAA,OAAI;;8CACH,mTAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,mTAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,mTAAC,+RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,mTAAC,+IAAA,CAAA,cAAW;;sDACV,mTAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,mTAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,mTAAC,+IAAA,CAAA,OAAI;;8CACH,mTAAC,+IAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,mTAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,mTAAC,qRAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,mTAAC,+IAAA,CAAA,cAAW;;sDACV,mTAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,mTAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,mTAAC;oBAAI,WAAU;8BACb,cAAA,mTAAC,iJAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;AAKvB;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,uMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,2IAAA,CAAA,aAAU,CAAC,UAAU;IAE1D,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,WAAW;YAC1B,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAS;QAAW;KAAO;IAE/B,IAAI,WAAW;QACb,qBAAO,mTAAC;sBAAI;;;;;;IACd;IAEA,qBACE,mTAAC,kJAAA,CAAA,mBAAgB;kBACf,cAAA,mTAAC;;;;;;;;;;AAGP", "debugId": null}}]}