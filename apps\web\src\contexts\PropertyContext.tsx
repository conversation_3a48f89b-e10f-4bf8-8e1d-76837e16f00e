"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { useProperties, type Property, type PropertyResponse } from "@/hooks/useProperties";

interface PropertyContextType {
  properties: Property[];
  isLoading: boolean;
  error: Error | null;
  propertyCount: number;
  refetch: () => void;
}

const PropertyContext = createContext<PropertyContextType | undefined>(undefined);

export const PropertyProvider = ({ children }: { children: ReactNode }) => {
  const { 
    data: propertiesResponse, 
    isLoading, 
    error,
    refetch 
  } = useProperties();

  const properties = propertiesResponse?.success ? propertiesResponse.data : [];
  const propertyCount = properties.length;

  const value: PropertyContextType = {
    properties,
    isLoading,
    error,
    propertyCount,
    refetch,
  };

  return (
    <PropertyContext.Provider value={value}>
      {children}
    </PropertyContext.Provider>
  );
};

export const usePropertyContext = () => {
  const context = useContext(PropertyContext);
  if (context === undefined) {
    throw new Error("usePropertyContext must be used within a PropertyProvider");
  }
  return context;
};
